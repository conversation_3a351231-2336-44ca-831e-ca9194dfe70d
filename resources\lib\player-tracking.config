minTimeToMatch=0.5
maxTimeToMatch=1.0
maxTimeToLink=10.0
maxOverlappingTimeToLink=0.0
maxTimeDiffToBeClose=0.2
minTimeValidToBeGroup=0.2
maxTimeToUpdateAppearanceContinuously=0.36
minTimeDiffToUpdateAppearance=0.12
maxTimeToKeepAppearance=2.0
maxIOUToUpdateAppearance=1.0
highConfidenceThreshold=0.5
highMatchingThreshold=0.4
lowMatchingThreshold=0.2
minAppearanceAffinity=0.4
minTimeToKeepTracks=1.0
confidenceDefaultValue=0.25
confidenceAttenuation=0.85
detectorAccuracy=1.2
occlusionBoxScaleFactor=1.5
minIOUToOcclude=0.02
kalmanFilterPositionWeight=0.05
kalmanFilterVelocityWeight=0.00625
maxTimeToInterpolate=1.0
timeToSmooth=0.44
maxTimeToComputeKalmanFilterMotion=1.0
varianceMotionFactor=1.5
varianceDetectionFactor=1.1
exponentialMovingAverageAlphaToComputeMotionVariance=0.2
maxDetectionsToComputeMotionVarianceAverage=5
mergeableTracksPerTrackFactor=1.5
mergeableTracksInTotalFactor=3.0
absoluteLimitsFactor=0.05
appearanceThresholdInCenterSeeds=0.5
nCenterSeeds=2
minFeaturesInCenterSeeds=4
minRatioValidFramesInClustering=0.5
timeToGatherClustering=0.8
timeToUpdateClustering=8.0
bufferMedoidsSize=300
minNumberOfPlayerToUpdateMedoids=3
timeToValidateClusteringForInconsistency=10000000
